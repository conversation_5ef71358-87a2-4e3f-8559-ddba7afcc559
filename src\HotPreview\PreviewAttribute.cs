using System;

namespace HotPreview;

/// <summary>
/// An attribute that specifies this method (which must be static) creates a preview for a UI component.
///
/// The method should return the preview UI object and the Hot Preview platform code
/// will then navigate to that preview. If the method navigates to the preview itself (often true
/// for a single page app) it can return void.
/// </summary>
/// <param name="displayName">Optional override for the display name of the preview, determining how it appears in the navigation UI.
/// If not specified, the name of the method (or class, for class-based previews) is used, converted to start case
/// (e.g. "MyPreview" becomes "My Preview"). Storybook also uses this start case convention.</param>
/// <param name="uiComponent">Optional parameter to override the UI component that the preview is associated with.
/// If not specified, then by default the UI component type is assumed to be the method return type or,
/// if the method returns void, then the containing class.</param>
[AttributeUsage(AttributeTargets.Class | AttributeTargets.Method)]
public sealed class PreviewAttribute(string? displayName = null, Type? uiComponent = null) : Attribute
{
    public static string TypeFullName => NameUtilities.NormalizeTypeFullName(typeof(PreviewAttribute));

    /// <summary>
    /// Optional override for the display name of the preview, determining how it appears in the navigation UI.
    /// If not specified, the name of the method (or class, for class-based previews) is used, converted to
    /// start case (e.g. "MyPreview" becomes "My Preview"). Storybook also uses this start case convention.
    /// </summary>
    public string? DisplayName { get; } = displayName;

    public Type? UIComponentType { get; } = uiComponent;
}

[AttributeUsage(AttributeTargets.Class | AttributeTargets.Method)]
public sealed class PreviewAttribute<TUIComponent>(string? displayName = null) : Attribute
{
    public static string TypeFullName => NameUtilities.NormalizeTypeFullName(typeof(PreviewAttribute));

    /// <summary>
    /// Optional override for the display name of the preview, determining how it appears in the navigation UI.
    /// If not specified, the name of the method (or class, for class-based previews) is used, converted to
    /// start case (e.g. "MyPreview" becomes "My Preview"). Storybook also uses this start case convention.
    /// </summary>
    public string? DisplayName { get; } = displayName;

    public Type UIComponentType { get; } = typeof(TUIComponent);
}
