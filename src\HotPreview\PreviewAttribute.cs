namespace HotPreview;

/// <summary>
/// An attribute that specifies this is a preview, for a control or other UI.
/// Previews can be shown in a gallery viewer, doc, etc.
/// </summary>
[AttributeUsage(AttributeTargets.Class | AttributeTargets.Method)]
public sealed class PreviewAttribute : Attribute
{
    public static string TypeFullName => NameUtilities.NormalizeTypeFullName(typeof(PreviewAttribute));

    /// <summary>
    /// Optional override for the display name of the preview, determining how it appears in the navigation UI.
    /// If not specified, the name of the method (or class, for class-based previews) is used, converted to
    /// start case (e.g. "MyPreview" becomes "My Preview"). Storybook also uses this start case convention.
    /// </summary>
    public string? DisplayName { get; }

    public Type? UIComponentType { get; }

    public PreviewAttribute()
    {
    }

    public PreviewAttribute(string? displayName = null, Type? uiComponent = null)
    {
        DisplayName = displayName;
        UIComponentType = uiComponent;
    }

    public PreviewAttribute(Type uiComponent)
    {
        UIComponentType = uiComponent;
    }
}
